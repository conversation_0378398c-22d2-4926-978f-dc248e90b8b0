import openai

# client = openai.Client(
#     base_url = "https://api.openai.com/v1", api_key="********************************************************************************************************************************************************************")

# completion = client.chat.completions.create(
#   model="gpt-4o-mini",
#   messages=[
#     {"role": "user", "content": "Hello!"}
#   ]
# )

client = openai.Client(
    base_url = "http://172.30.56.1:4000/v1", api_key="sk-1234")

completion = client.chat.completions.create(
  model="Qwen/Qwen2.5-32B-Instruct",
  messages=[
    {"role": "system", "content": "You are a user interacting with an agent. The agent is a customer service representative for an online store. The user is trying to get help with an order. The user is friendly and polite, while the agent is professional and helpful. The user has a specific order they need assistance with, but they don't remember the order ID. The agent should ask for the order ID to assist the user further. The conversation should be natural and flow smoothly."},
    {"role": "user", "content": "Hello!"}
  ],
  # tool_choice='none'
)

print(completion.choices[0].message)

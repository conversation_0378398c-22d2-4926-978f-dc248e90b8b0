# user_model 使用固定的 gpt-4o，方便各个模型之间 user 能够对齐

export DEPLOY_API_KEY=YOUR_API_KEY
export DEPLOY_BASE_URL=http://172.30.52.140:23333/v1

export OPENAI_API_KEY=********************************************************************************************************************************************************************
# OPENAI_API_KEY=********************************************************************************************************************************************************************
export OPENAI_PROXY_URL=http://closeai-proxy.pjlab.org.cn:23128

python run.py \
    --agent-strategy react \
    --env airline \
    --model gpt-4o \
    --model-provider openai \
    --user-model gpt-4o \
    --user-model-provider openai \
    --user-strategy llm \
    --max-concurrency 10

#!/bin/bash
# 支持多模型顺序执行的脚本

# 默认参数设置
AGENT_STRATEGY="react"
ENV="retail"
USER_MODEL="gpt-4o"
USER_MODEL_PROVIDER="openai"
USER_STRATEGY="llm"
MAX_CONCURRENCY=10
MODEL_PROVIDER="deployed"

# OpenAI API 设置
export OPENAI_API_KEY=********************************************************************************************************************************************************************
export OPENAI_PROXY_URL=http://closeai-proxy.pjlab.org.cn:23128

# 默认 API KEY
DEFAULT_DEPLOY_API_KEY="YOUR_API_KEY"

# 模型列表和对应的 BASE_URL 配置
# 可以在这里添加更多模型和对应的 BASE_URL
declare -A MODEL_BASE_URLS
MODEL_BASE_URLS["Qwen/Qwen2.5-14B-Instruct"]="http://172.30.52.140:23333/v1"
MODEL_BASE_URLS["Qwen/Qwen2.5-7B-Instruct"]="http://172.30.52.140:23334/v1"
MODEL_BASE_URLS["Qwen/Qwen2.5-4B-Instruct"]="http://172.30.52.140:23335/v1"
# 添加更多模型和对应的 BASE_URL

# 模型列表和对应的 API_KEY 配置
# 可以在这里添加更多模型和对应的 API_KEY
declare -A MODEL_API_KEYS
MODEL_API_KEYS["Qwen/Qwen2.5-14B-Instruct"]="YOUR_API_KEY"
MODEL_API_KEYS["Qwen/Qwen2.5-7B-Instruct"]="YOUR_API_KEY"
MODEL_API_KEYS["Qwen/Qwen2.5-4B-Instruct"]="YOUR_API_KEY"
# 添加更多模型和对应的 API_KEY

# 解析命令行参数中的模型列表
MODELS=()
for arg in "$@"; do
    MODELS+=("$arg")
done

# 如果没有提供模型，使用默认模型
if [ ${#MODELS[@]} -eq 0 ]; then
    MODELS=("Qwen/Qwen2.5-14B-Instruct")
fi

# 显示将要执行的模型
echo "将按顺序执行以下模型:"
for MODEL in "${MODELS[@]}"; do
    echo "- $MODEL"
done
echo ""

# 循环执行每个模型
for MODEL in "${MODELS[@]}"; do
    echo "========================================"
    echo "开始执行模型: $MODEL"

    # 获取当前模型对应的 BASE_URL
    BASE_URL="${MODEL_BASE_URLS[$MODEL]}"

    # 如果没有找到对应的 BASE_URL，使用默认值
    if [ -z "$BASE_URL" ]; then
        echo "警告: 未找到模型 $MODEL 的 BASE_URL 配置，使用默认值"
        BASE_URL="http://172.30.52.140:23333/v1"
    fi

    # 获取当前模型对应的 API_KEY
    API_KEY="${MODEL_API_KEYS[$MODEL]}"

    # 如果没有找到对应的 API_KEY，使用默认值
    if [ -z "$API_KEY" ]; then
        echo "警告: 未找到模型 $MODEL 的 API_KEY 配置，使用默认值"
        API_KEY="$DEFAULT_DEPLOY_API_KEY"
    fi

    echo "使用 BASE_URL: $BASE_URL"
    echo "使用 API_KEY: $API_KEY"
    echo "========================================"

    # 为当前模型设置 DEPLOY_BASE_URL 和 DEPLOY_API_KEY
    export DEPLOY_BASE_URL="$BASE_URL"
    export DEPLOY_API_KEY="$API_KEY"

    python run.py \
        --agent-strategy $AGENT_STRATEGY \
        --env $ENV \
        --model "$MODEL" \
        --model-provider $MODEL_PROVIDER \
        --user-model $USER_MODEL \
        --user-model-provider $USER_MODEL_PROVIDER \
        --user-strategy $USER_STRATEGY \
        --max-concurrency $MAX_CONCURRENCY
done

echo "所有模型执行完毕!"
